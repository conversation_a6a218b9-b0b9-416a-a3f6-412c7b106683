'use client';

import React, { useState } from 'react';
import { ColorRecommendation, FeedbackData } from '@/types';
import { getContrastColor } from '@/lib/utils';
import { 
  Palette, 
  Shirt, 
  Star, 
  Heart, 
  ThumbsUp, 
  ThumbsDown,
  Sparkles,
  Info
} from 'lucide-react';
import { LoadingButton } from '@/components/ui/LoadingSpinner';

interface ColorRecommendationsProps {
  recommendation: ColorRecommendation;
  onFeedback?: (feedback: FeedbackData) => void;
  isSubmittingFeedback?: boolean;
}

export const ColorRecommendations: React.FC<ColorRecommendationsProps> = ({
  recommendation,
  onFeedback,
  isSubmittingFeedback = false,
}) => {
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [favoriteOutfits, setFavoriteOutfits] = useState<number[]>([]);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);

  const { recommendations, colorPalette, generalAdvice } = recommendation;

  const handleOutfitFavorite = (index: number) => {
    setFavoriteOutfits(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const handleSubmitFeedback = () => {
    if (onFeedback && rating > 0) {
      onFeedback({
        rating,
        feedback,
        favoriteOutfits,
      });
      setShowFeedbackForm(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Sparkles className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Your Color Recommendations</h2>
        <p className="text-gray-600">Personalized outfit suggestions based on your unique features</p>
      </div>

      {/* Seasonal Type & General Advice */}
      <div className="card bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <div className="flex items-start space-x-3">
          <Info className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h3 className="font-semibold text-blue-900 mb-2">
              Your Seasonal Type: {colorPalette.seasonalType}
            </h3>
            <p className="text-blue-800">{generalAdvice}</p>
          </div>
        </div>
      </div>

      {/* Color Palette */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <Palette className="w-6 h-6 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">Your Color Palette</h3>
        </div>
        
        <div className="space-y-4">
          {/* Best Colors */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <ThumbsUp className="w-4 h-4 text-green-600" />
              <span>Colors that enhance your features</span>
            </h4>
            <div className="flex flex-wrap gap-3">
              {colorPalette.bestColors.map((color, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-10 h-10 rounded-lg border border-gray-200 shadow-sm"
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm font-medium text-gray-700">{color}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Colors to Avoid */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <ThumbsDown className="w-4 h-4 text-red-600" />
              <span>Colors to use sparingly</span>
            </h4>
            <div className="flex flex-wrap gap-3">
              {colorPalette.avoidColors.map((color, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-10 h-10 rounded-lg border border-gray-200 shadow-sm opacity-60"
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm font-medium text-gray-500">{color}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Outfit Recommendations */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <Shirt className="w-6 h-6 text-purple-600" />
          <span>Outfit Recommendations</span>
        </h3>
        
        {recommendations.map((outfit, index) => (
          <div key={index} className="card hover:shadow-lg transition-shadow duration-200">
            <div className="flex items-start justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">{outfit.outfitName}</h4>
              <button
                onClick={() => handleOutfitFavorite(index)}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  favoriteOutfits.includes(index)
                    ? 'bg-red-100 text-red-600'
                    : 'bg-gray-100 text-gray-400 hover:text-red-600'
                }`}
              >
                <Heart className={`w-5 h-5 ${favoriteOutfits.includes(index) ? 'fill-current' : ''}`} />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Shirt */}
              <div className="space-y-3">
                <h5 className="font-medium text-gray-700">Shirt</h5>
                <div className="flex items-center space-x-3">
                  <div
                    className="w-12 h-12 rounded-lg border border-gray-200 shadow-sm"
                    style={{ backgroundColor: outfit.shirt.hex }}
                  />
                  <div>
                    <p className="font-medium" style={{ color: outfit.shirt.hex }}>
                      {outfit.shirt.color}
                    </p>
                    <p className="text-sm text-gray-600">{outfit.shirt.reason}</p>
                  </div>
                </div>
              </div>

              {/* Pants */}
              <div className="space-y-3">
                <h5 className="font-medium text-gray-700">Pants</h5>
                <div className="flex items-center space-x-3">
                  <div
                    className="w-12 h-12 rounded-lg border border-gray-200 shadow-sm"
                    style={{ backgroundColor: outfit.pants.hex }}
                  />
                  <div>
                    <p className="font-medium" style={{ color: outfit.pants.hex }}>
                      {outfit.pants.color}
                    </p>
                    <p className="text-sm text-gray-600">{outfit.pants.reason}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Feedback Section */}
      {onFeedback && (
        <div className="card bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Rate These Recommendations</h3>
          
          {!showFeedbackForm ? (
            <button
              onClick={() => setShowFeedbackForm(true)}
              className="btn-primary"
            >
              Provide Feedback
            </button>
          ) : (
            <div className="space-y-4">
              {/* Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  How helpful were these recommendations?
                </label>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => setRating(star)}
                      className={`p-1 ${
                        star <= rating ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      <Star className={`w-6 h-6 ${star <= rating ? 'fill-current' : ''}`} />
                    </button>
                  ))}
                </div>
              </div>

              {/* Feedback Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Comments (Optional)
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className="input-field h-20 resize-none"
                  placeholder="Tell us what you think about these recommendations..."
                />
              </div>

              {/* Favorite Outfits */}
              <div>
                <p className="text-sm text-gray-600 mb-2">
                  You've marked {favoriteOutfits.length} outfit{favoriteOutfits.length !== 1 ? 's' : ''} as favorite
                </p>
              </div>

              {/* Submit Buttons */}
              <div className="flex space-x-3">
                <LoadingButton
                  onClick={handleSubmitFeedback}
                  isLoading={isSubmittingFeedback}
                  disabled={rating === 0}
                >
                  Submit Feedback
                </LoadingButton>
                <button
                  onClick={() => setShowFeedbackForm(false)}
                  className="btn-secondary"
                  disabled={isSubmittingFeedback}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
