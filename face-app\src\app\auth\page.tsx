'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { RegisterForm } from '@/components/auth/RegisterForm';
import { LoginForm } from '@/components/auth/LoginForm';
import { OTPForm } from '@/components/auth/OTPForm';
import { LoadingOverlay } from '@/components/ui/LoadingSpinner';
import { Palette, Sparkles, Users, Shield } from 'lucide-react';

type AuthStep = 'login' | 'register' | 'otp';

export default function AuthPage() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();
  const [currentStep, setCurrentStep] = useState<AuthStep>('login');
  const [registrationEmail, setRegistrationEmail] = useState('');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  const handleRegistrationSuccess = (email: string) => {
    setRegistrationEmail(email);
    setCurrentStep('otp');
  };

  const handleOTPSuccess = () => {
    setCurrentStep('login');
  };

  const handleLoginSuccess = () => {
    router.push('/dashboard');
  };

  if (loading.isLoading) {
    return <LoadingOverlay message={loading.message} />;
  }

  if (isAuthenticated) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen gradient-bg flex">
      {/* Left Side - Features */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white p-12 flex-col justify-center">
        <div className="max-w-md">
          <h1 className="text-4xl font-bold mb-6">
            Discover Your Perfect Colors
          </h1>
          <p className="text-xl mb-8 text-blue-100">
            Get personalized color recommendations based on your unique facial features and skin tone.
          </p>
          
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <Palette className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">AI-Powered Analysis</h3>
                <p className="text-blue-100">
                  Advanced facial recognition technology analyzes your skin tone and features
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <Sparkles className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Personalized Recommendations</h3>
                <p className="text-blue-100">
                  Get outfit suggestions tailored to your unique color palette
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <Users className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Style Community</h3>
                <p className="text-blue-100">
                  Join thousands of users discovering their perfect style
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <Shield className="w-6 h-6" />
              </div>
              <div>
                <h3 className="font-semibold mb-1">Privacy First</h3>
                <p className="text-blue-100">
                  Your photos are processed securely and never shared
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Auth Forms */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {currentStep === 'login' && (
            <LoginForm
              onSuccess={handleLoginSuccess}
              onSwitchToRegister={() => setCurrentStep('register')}
            />
          )}
          
          {currentStep === 'register' && (
            <RegisterForm
              onSuccess={handleRegistrationSuccess}
              onSwitchToLogin={() => setCurrentStep('login')}
            />
          )}
          
          {currentStep === 'otp' && (
            <OTPForm
              email={registrationEmail}
              onSuccess={handleOTPSuccess}
              onBack={() => setCurrentStep('register')}
            />
          )}
        </div>
      </div>
    </div>
  );
}
