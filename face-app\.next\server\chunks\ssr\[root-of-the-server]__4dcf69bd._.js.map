{"version": 3, "sources": [], "sections": [{"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosResponse, AxiosError } from 'axios';\nimport {\n  ApiResponse,\n  AuthResponse,\n  RegisterData,\n  LoginData,\n  OTPData,\n  User,\n  FaceAnalysis,\n  AnalysisRequest,\n  ColorRecommendation,\n  RecommendationRequest,\n  UploadSignature,\n  FeedbackData,\n} from '@/types';\n\nconst API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response: AxiosResponse) => response,\n  (error: AxiosError<ApiResponse>) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('authToken');\n      window.location.href = '/auth/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API functions\nexport const authAPI = {\n  register: async (data: RegisterData): Promise<ApiResponse<{ user: User }>> => {\n    const response = await api.post('/auth/register', data);\n    return response.data;\n  },\n\n  verifyOTP: async (data: OTPData): Promise<ApiResponse> => {\n    const response = await api.post('/auth/verify-email-otp', data);\n    return response.data;\n  },\n\n  login: async (data: LoginData): Promise<ApiResponse<AuthResponse>> => {\n    const response = await api.post('/auth/login', data);\n    return response.data;\n  },\n\n  getProfile: async (): Promise<ApiResponse<User>> => {\n    const response = await api.get('/auth/me');\n    return response.data;\n  },\n\n  logout: async (): Promise<ApiResponse> => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n\n  resendOTP: async (email: string): Promise<ApiResponse> => {\n    const response = await api.post('/auth/resend-verification', { email });\n    return response.data;\n  },\n};\n\n// Face Analysis API functions\nexport const faceAPI = {\n  analyzeFace: async (data: AnalysisRequest): Promise<ApiResponse<{ analysis: FaceAnalysis }>> => {\n    const response = await api.post('/face/analyze-direct', data);\n    return response.data;\n  },\n\n  getHistory: async (page = 1, limit = 10): Promise<ApiResponse<{ analyses: FaceAnalysis[] }>> => {\n    const response = await api.get(`/face/history?page=${page}&limit=${limit}`);\n    return response.data;\n  },\n\n  getAnalysis: async (id: string): Promise<ApiResponse<FaceAnalysis>> => {\n    const response = await api.get(`/face/analysis/${id}`);\n    return response.data;\n  },\n\n  deleteAnalysis: async (id: string): Promise<ApiResponse> => {\n    const response = await api.delete(`/face/analysis/${id}`);\n    return response.data;\n  },\n};\n\n// Color Recommendation API functions\nexport const recommendationAPI = {\n  getRecommendations: async (\n    analysisId: string,\n    preferences: RecommendationRequest['preferences']\n  ): Promise<ApiResponse<ColorRecommendation>> => {\n    const response = await api.post(`/face/analysis/${analysisId}/recommendations`, { preferences });\n    return response.data;\n  },\n\n  getLatestRecommendation: async (): Promise<ApiResponse<ColorRecommendation>> => {\n    const response = await api.get('/face/recommendations/latest');\n    return response.data;\n  },\n\n  getRecommendationHistory: async (limit = 10): Promise<ApiResponse<ColorRecommendation[]>> => {\n    const response = await api.get(`/face/recommendations/history?limit=${limit}`);\n    return response.data;\n  },\n\n  addFeedback: async (\n    recommendationId: string,\n    feedback: FeedbackData\n  ): Promise<ApiResponse> => {\n    const response = await api.post(`/face/recommendations/${recommendationId}/feedback`, feedback);\n    return response.data;\n  },\n};\n\n// Upload API functions\nexport const uploadAPI = {\n  getUploadSignature: async (): Promise<ApiResponse<UploadSignature>> => {\n    const response = await api.post('/upload/mobile-signature');\n    return response.data;\n  },\n\n  getUploadConfig: async (): Promise<ApiResponse> => {\n    const response = await api.get('/upload/config');\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthAPI = {\n  check: async (): Promise<ApiResponse> => {\n    const response = await axios.get(`${API_BASE_URL}/health`);\n    return response.data;\n  },\n\n  ping: async (): Promise<string> => {\n    const response = await axios.get(`${API_BASE_URL}/ping`);\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAgBA,MAAM,eAAe;AAErB,wBAAwB;AACxB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC5B,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT;AAEA,0CAA0C;AAC1C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAA4B,UAC7B,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2BAA2B;QAC3B,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kBAAkB;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,eAAe;QAC/C,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;QACV,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;YAAE;QAAM;QACrE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,KAAK,OAAO,EAAE,OAAO;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QACxD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,oBAAoB;IAC/B,oBAAoB,OAClB,YACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,WAAW,gBAAgB,CAAC,EAAE;YAAE;QAAY;QAC9F,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B,OAAO,QAAQ,EAAE;QACzC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oCAAoC,EAAE,OAAO;QAC7E,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OACX,kBACA;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,sBAAsB,EAAE,iBAAiB,SAAS,CAAC,EAAE;QACtF,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;QAClB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;QACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,OAAO;QACL,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,OAAO,CAAC;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM;QACJ,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,KAAK,CAAC;QACvD,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { User, LoadingState } from '@/types';\nimport { authAPI } from '@/lib/api';\nimport toast from 'react-hot-toast';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: LoadingState;\n  login: (email: string, password: string) => Promise<boolean>;\n  logout: () => Promise<void>;\n  register: (name: string, email: string, password: string, gender: string) => Promise<boolean>;\n  verifyOTP: (email: string, otp: string) => Promise<boolean>;\n  resendOTP: (email: string) => Promise<boolean>;\n  isAuthenticated: boolean;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState<LoadingState>({ isLoading: true });\n\n  // Check if user is authenticated on mount\n  useEffect(() => {\n    checkAuthStatus();\n  }, []);\n\n  const checkAuthStatus = async () => {\n    const token = localStorage.getItem('authToken');\n    if (!token) {\n      setLoading({ isLoading: false });\n      return;\n    }\n\n    try {\n      setLoading({ isLoading: true, message: 'Checking authentication...' });\n      const response = await authAPI.getProfile();\n      if (response.success && response.data) {\n        setUser(response.data);\n      } else {\n        localStorage.removeItem('authToken');\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      localStorage.removeItem('authToken');\n    } finally {\n      setLoading({ isLoading: false });\n    }\n  };\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    try {\n      setLoading({ isLoading: true, message: 'Signing in...' });\n      const response = await authAPI.login({ email, password });\n      \n      if (response.success && response.data) {\n        localStorage.setItem('authToken', response.data.token);\n        setUser(response.data.user);\n        toast.success('Login successful!');\n        return true;\n      } else {\n        toast.error(response.message || 'Login failed');\n        return false;\n      }\n    } catch (error: any) {\n      const message = error.response?.data?.message || 'Login failed';\n      toast.error(message);\n      return false;\n    } finally {\n      setLoading({ isLoading: false });\n    }\n  };\n\n  const register = async (\n    name: string,\n    email: string,\n    password: string,\n    gender: string\n  ): Promise<boolean> => {\n    try {\n      setLoading({ isLoading: true, message: 'Creating account...' });\n      const response = await authAPI.register({\n        name,\n        email,\n        password,\n        gender: gender as any,\n      });\n      \n      if (response.success) {\n        toast.success(response.message || 'Registration successful! Please check your email for OTP.');\n        return true;\n      } else {\n        toast.error(response.message || 'Registration failed');\n        return false;\n      }\n    } catch (error: any) {\n      const message = error.response?.data?.message || 'Registration failed';\n      toast.error(message);\n      return false;\n    } finally {\n      setLoading({ isLoading: false });\n    }\n  };\n\n  const verifyOTP = async (email: string, otp: string): Promise<boolean> => {\n    try {\n      setLoading({ isLoading: true, message: 'Verifying OTP...' });\n      const response = await authAPI.verifyOTP({ email, otp });\n      \n      if (response.success) {\n        toast.success('Email verified successfully!');\n        return true;\n      } else {\n        toast.error(response.message || 'OTP verification failed');\n        return false;\n      }\n    } catch (error: any) {\n      const message = error.response?.data?.message || 'OTP verification failed';\n      toast.error(message);\n      return false;\n    } finally {\n      setLoading({ isLoading: false });\n    }\n  };\n\n  const resendOTP = async (email: string): Promise<boolean> => {\n    try {\n      setLoading({ isLoading: true, message: 'Resending OTP...' });\n      const response = await authAPI.resendOTP(email);\n      \n      if (response.success) {\n        toast.success(response.message || 'OTP sent successfully!');\n        return true;\n      } else {\n        toast.error(response.message || 'Failed to resend OTP');\n        return false;\n      }\n    } catch (error: any) {\n      const message = error.response?.data?.message || 'Failed to resend OTP';\n      toast.error(message);\n      return false;\n    } finally {\n      setLoading({ isLoading: false });\n    }\n  };\n\n  const logout = async () => {\n    try {\n      setLoading({ isLoading: true, message: 'Signing out...' });\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      localStorage.removeItem('authToken');\n      setUser(null);\n      setLoading({ isLoading: false });\n      toast.success('Logged out successfully');\n    }\n  };\n\n  const refreshUser = async () => {\n    try {\n      const response = await authAPI.getProfile();\n      if (response.success && response.data) {\n        setUser(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to refresh user:', error);\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    logout,\n    register,\n    verifyOTP,\n    resendOTP,\n    isAuthenticated: !!user,\n    refreshUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAAE,WAAW;IAAK;IAEvE,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,WAAW;gBAAE,WAAW;YAAM;YAC9B;QACF;QAEA,IAAI;YACF,WAAW;gBAAE,WAAW;gBAAM,SAAS;YAA6B;YACpE,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU;YACzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;YACvB,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,WAAW;gBAAE,WAAW;YAAM;QAChC;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,WAAW;gBAAE,WAAW;gBAAM,SAAS;YAAgB;YACvD,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAEvD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,OAAO,CAAC,aAAa,SAAS,IAAI,CAAC,KAAK;gBACrD,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;YACjD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,WAAW;gBAAE,WAAW;YAAM;QAChC;IACF;IAEA,MAAM,WAAW,OACf,MACA,OACA,UACA;QAEA,IAAI;YACF,WAAW;gBAAE,WAAW;gBAAM,SAAS;YAAsB;YAC7D,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;gBACtC;gBACA;gBACA;gBACA,QAAQ;YACV;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBAClC,OAAO;YACT,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;YACjD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,WAAW;gBAAE,WAAW;YAAM;QAChC;IACF;IAEA,MAAM,YAAY,OAAO,OAAe;QACtC,IAAI;YACF,WAAW;gBAAE,WAAW;gBAAM,SAAS;YAAmB;YAC1D,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;gBAAE;gBAAO;YAAI;YAEtD,IAAI,SAAS,OAAO,EAAE;gBACpB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;YACjD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,WAAW;gBAAE,WAAW;YAAM;QAChC;IACF;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,WAAW;gBAAE,WAAW;gBAAM,SAAS;YAAmB;YAC1D,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;YAEzC,IAAI,SAAS,OAAO,EAAE;gBACpB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBAClC,OAAO;YACT,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,OAAO;YACT;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,WAAW;YACjD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT,SAAU;YACR,WAAW;gBAAE,WAAW;YAAM;QAChC;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,WAAW;gBAAE,WAAW;gBAAM,SAAS;YAAiB;YACxD,MAAM,iHAAA,CAAA,UAAO,CAAC,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa,UAAU,CAAC;YACxB,QAAQ;YACR,WAAW;gBAAE,WAAW;YAAM;YAC9B,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU;YACzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { AlertTriangle, RefreshCw, Home } from 'lucide-react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\nexport class ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n    this.setState({ error, errorInfo });\n  }\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  handleGoHome = () => {\n    window.location.href = '/dashboard';\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-4\">\n          <div className=\"max-w-md w-full\">\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <AlertTriangle className=\"w-8 h-8 text-red-600\" />\n              </div>\n              \n              <h1 className=\"text-xl font-bold text-gray-900 mb-2\">\n                Something went wrong\n              </h1>\n              \n              <p className=\"text-gray-600 mb-6\">\n                We're sorry, but something unexpected happened. Please try refreshing the page or go back to the dashboard.\n              </p>\n\n              {process.env.NODE_ENV === 'development' && this.state.error && (\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left\">\n                  <h3 className=\"font-medium text-red-900 mb-2\">Error Details:</h3>\n                  <pre className=\"text-sm text-red-800 whitespace-pre-wrap overflow-auto max-h-32\">\n                    {this.state.error.message}\n                  </pre>\n                </div>\n              )}\n\n              <div className=\"flex flex-col sm:flex-row gap-3\">\n                <button\n                  onClick={this.handleReset}\n                  className=\"btn-primary flex items-center justify-center space-x-2\"\n                >\n                  <RefreshCw className=\"w-4 h-4\" />\n                  <span>Try Again</span>\n                </button>\n                \n                <button\n                  onClick={this.handleGoHome}\n                  className=\"btn-secondary flex items-center justify-center space-x-2\"\n                >\n                  <Home className=\"w-4 h-4\" />\n                  <span>Go to Dashboard</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Hook version for functional components\nexport const useErrorHandler = () => {\n  const handleError = (error: Error, errorInfo?: ErrorInfo) => {\n    console.error('Error handled:', error, errorInfo);\n    // You could send this to an error reporting service\n  };\n\n  return handleError;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAgBO,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IAC1C,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,QAAQ,KAAK,CAAC,6BAA6B,OAAO;QAClD,IAAI,CAAC,QAAQ,CAAC;YAAE;YAAO;QAAU;IACnC;IAEA,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;YAAW,WAAW;QAAU;IAC1E,EAAE;IAEF,eAAe;QACb,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG3B,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAIrD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;4BAIjC,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;kDACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAI,CAAC,WAAW;wCACzB,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAI,CAAC,YAAY;wCAC1B,WAAU;;0DAEV,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAOpB;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,CAAC,OAAc;QACjC,QAAQ,KAAK,CAAC,kBAAkB,OAAO;IACvC,oDAAoD;IACtD;IAEA,OAAO;AACT", "debugId": null}}]}