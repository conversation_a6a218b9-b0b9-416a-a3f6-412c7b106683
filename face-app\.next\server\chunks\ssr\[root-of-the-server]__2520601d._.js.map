{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { uploadAPI } from './api';\nimport { CloudinaryUploadResult } from '@/types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Image validation\nexport const validateImageFile = (file: File): { isValid: boolean; error?: string } => {\n  const maxSize = 10 * 1024 * 1024; // 10MB\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n  if (!allowedTypes.includes(file.type)) {\n    return {\n      isValid: false,\n      error: 'Please select a valid image file (JPEG, PNG, or WebP)',\n    };\n  }\n\n  if (file.size > maxSize) {\n    return {\n      isValid: false,\n      error: 'Image size must be less than 10MB',\n    };\n  }\n\n  return { isValid: true };\n};\n\n// Upload image to Cloudinary\nexport const uploadImageToCloudinary = async (file: File): Promise<CloudinaryUploadResult> => {\n  try {\n    // Get upload signature from backend\n    const signatureResponse = await uploadAPI.getUploadSignature();\n    if (!signatureResponse.success || !signatureResponse.data) {\n      throw new Error('Failed to get upload signature');\n    }\n\n    const { signature, timestamp, apiKey, uploadUrl, folder } = signatureResponse.data;\n\n    // Create form data for Cloudinary upload\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('signature', signature);\n    formData.append('timestamp', timestamp.toString());\n    formData.append('api_key', apiKey);\n    formData.append('folder', folder);\n\n    // Upload to Cloudinary\n    const uploadResponse = await fetch(uploadUrl, {\n      method: 'POST',\n      body: formData,\n    });\n\n    if (!uploadResponse.ok) {\n      throw new Error('Failed to upload image');\n    }\n\n    const result: CloudinaryUploadResult = await uploadResponse.json();\n    return result;\n  } catch (error) {\n    console.error('Upload error:', error);\n    throw new Error('Failed to upload image. Please try again.');\n  }\n};\n\n// Format date\nexport const formatDate = (dateString: string): string => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\n// Format confidence percentage\nexport const formatConfidence = (confidence: number): string => {\n  return `${(confidence * 100).toFixed(1)}%`;\n};\n\n// Color utilities\nexport const isLightColor = (hex: string): boolean => {\n  const r = parseInt(hex.slice(1, 3), 16);\n  const g = parseInt(hex.slice(3, 5), 16);\n  const b = parseInt(hex.slice(5, 7), 16);\n  const brightness = (r * 299 + g * 587 + b * 114) / 1000;\n  return brightness > 128;\n};\n\nexport const getContrastColor = (hex: string): string => {\n  return isLightColor(hex) ? '#000000' : '#ffffff';\n};\n\n// Debounce function\nexport const debounce = <T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n};\n\n// Local storage utilities\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null;\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue || null;\n    } catch {\n      return defaultValue || null;\n    }\n  },\n\n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return;\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error);\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return;\n    localStorage.removeItem(key);\n  },\n};\n\n// Error handling\nexport const getErrorMessage = (error: any): string => {\n  if (error?.response?.data?.message) {\n    return error.response.data.message;\n  }\n  if (error?.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n};\n\n// Validation utilities\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {\n  const errors: string[] = [];\n  \n  if (password.length < 6) {\n    errors.push('Password must be at least 6 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Generate random ID\nexport const generateId = (): string => {\n  return Math.random().toString(36).substr(2, 9);\n};\n\n// Copy to clipboard\nexport const copyToClipboard = async (text: string): Promise<boolean> => {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.select();\n    const success = document.execCommand('copy');\n    document.body.removeChild(textArea);\n    return success;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,oBAAoB,CAAC;IAChC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,oCAAoC;QACpC,MAAM,oBAAoB,MAAM,iHAAA,CAAA,YAAS,CAAC,kBAAkB;QAC5D,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,kBAAkB,IAAI;QAElF,yCAAyC;QACzC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,aAAa;QAC7B,SAAS,MAAM,CAAC,aAAa,UAAU,QAAQ;QAC/C,SAAS,MAAM,CAAC,WAAW;QAC3B,SAAS,MAAM,CAAC,UAAU;QAE1B,uBAAuB;QACvB,MAAM,iBAAiB,MAAM,MAAM,WAAW;YAC5C,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAiC,MAAM,eAAe,IAAI;QAChE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,GAAG,CAAC,aAAa,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5C;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACpC,MAAM,aAAa,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;IACnD,OAAO,aAAa;AACtB;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,OAAO,YAAY;AACzC;AAGO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;;IAO5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;;IAMrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;;IAErC;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,UAAU,MAAM,SAAS;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACpC;IACA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAM;QACN,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,MAAM;QACf,MAAM,UAAU,SAAS,WAAW,CAAC;QACrC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8',\n  };\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n};\n\ninterface LoadingOverlayProps {\n  message?: string;\n  className?: string;\n}\n\nexport const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ \n  message = 'Loading...', \n  className \n}) => {\n  return (\n    <div className={cn(\n      'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',\n      className\n    )}>\n      <div className=\"bg-white rounded-lg p-6 flex flex-col items-center space-y-4\">\n        <LoadingSpinner size=\"lg\" />\n        <p className=\"text-gray-700 font-medium\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\ninterface LoadingButtonProps {\n  isLoading: boolean;\n  children: React.ReactNode;\n  className?: string;\n  disabled?: boolean;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n}\n\nexport const LoadingButton: React.FC<LoadingButtonProps> = ({\n  isLoading,\n  children,\n  className,\n  disabled,\n  onClick,\n  type = 'button',\n}) => {\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || isLoading}\n      className={cn(\n        'btn-primary flex items-center justify-center space-x-2',\n        className\n      )}\n    >\n      {isLoading && <LoadingSpinner size=\"sm\" className=\"border-white border-t-transparent\" />}\n      <span>{children}</span>\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AACA;;;AAOO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;AAOO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,YAAY,EACtB,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8EACA;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAe,MAAK;;;;;;8BACrB,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;AAWO,MAAM,gBAA8C,CAAC,EAC1D,SAAS,EACT,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EAChB;IACC,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;;YAGD,2BAAa,8OAAC;gBAAe,MAAK;gBAAK,WAAU;;;;;;0BAClD,8OAAC;0BAAM;;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/components/auth/RegisterForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LoadingButton } from '@/components/ui/LoadingSpinner';\nimport { validateEmail, validatePassword } from '@/lib/utils';\nimport { Eye, EyeOff, User, Mail, Lock, Users } from 'lucide-react';\n\ninterface RegisterFormProps {\n  onSuccess: (email: string) => void;\n  onSwitchToLogin: () => void;\n}\n\nexport const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onSwitchToLogin }) => {\n  const { register, loading } = useAuth();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    gender: 'prefer_not_to_say',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters';\n    }\n\n    // Email validation\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Password validation\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else {\n      const passwordValidation = validatePassword(formData.password);\n      if (!passwordValidation.isValid) {\n        newErrors.password = passwordValidation.errors[0];\n      }\n    }\n\n    // Confirm password validation\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    const success = await register(\n      formData.name.trim(),\n      formData.email.trim(),\n      formData.password,\n      formData.gender\n    );\n\n    if (success) {\n      onSuccess(formData.email.trim());\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  return (\n    <div className=\"card max-w-md mx-auto\">\n      <div className=\"text-center mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Create Account</h2>\n        <p className=\"text-gray-600 mt-2\">Join Face App to get personalized color recommendations</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* Name Field */}\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Full Name\n          </label>\n          <div className=\"relative\">\n            <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              id=\"name\"\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => handleInputChange('name', e.target.value)}\n              className={`input-field pl-10 ${errors.name ? 'border-red-500' : ''}`}\n              placeholder=\"Enter your full name\"\n            />\n          </div>\n          {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n        </div>\n\n        {/* Email Field */}\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email Address\n          </label>\n          <div className=\"relative\">\n            <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={formData.email}\n              onChange={(e) => handleInputChange('email', e.target.value)}\n              className={`input-field pl-10 ${errors.email ? 'border-red-500' : ''}`}\n              placeholder=\"Enter your email\"\n            />\n          </div>\n          {errors.email && <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>}\n        </div>\n\n        {/* Gender Field */}\n        <div>\n          <label htmlFor=\"gender\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Gender\n          </label>\n          <div className=\"relative\">\n            <Users className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <select\n              id=\"gender\"\n              value={formData.gender}\n              onChange={(e) => handleInputChange('gender', e.target.value)}\n              className=\"input-field pl-10\"\n            >\n              <option value=\"male\">Male</option>\n              <option value=\"female\">Female</option>\n              <option value=\"other\">Other</option>\n              <option value=\"prefer_not_to_say\">Prefer not to say</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Password Field */}\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Password\n          </label>\n          <div className=\"relative\">\n            <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              id=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              value={formData.password}\n              onChange={(e) => handleInputChange('password', e.target.value)}\n              className={`input-field pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}\n              placeholder=\"Create a password\"\n            />\n            <button\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n            >\n              {showPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n            </button>\n          </div>\n          {errors.password && <p className=\"text-red-500 text-sm mt-1\">{errors.password}</p>}\n        </div>\n\n        {/* Confirm Password Field */}\n        <div>\n          <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Confirm Password\n          </label>\n          <div className=\"relative\">\n            <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              id=\"confirmPassword\"\n              type={showConfirmPassword ? 'text' : 'password'}\n              value={formData.confirmPassword}\n              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}\n              className={`input-field pl-10 pr-10 ${errors.confirmPassword ? 'border-red-500' : ''}`}\n              placeholder=\"Confirm your password\"\n            />\n            <button\n              type=\"button\"\n              onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n            >\n              {showConfirmPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n            </button>\n          </div>\n          {errors.confirmPassword && <p className=\"text-red-500 text-sm mt-1\">{errors.confirmPassword}</p>}\n        </div>\n\n        {/* Submit Button */}\n        <LoadingButton\n          type=\"submit\"\n          isLoading={loading.isLoading}\n          className=\"w-full\"\n        >\n          Create Account\n        </LoadingButton>\n      </form>\n\n      {/* Switch to Login */}\n      <div className=\"text-center mt-6\">\n        <p className=\"text-gray-600\">\n          Already have an account?{' '}\n          <button\n            onClick={onSwitchToLogin}\n            className=\"text-blue-600 hover:text-blue-700 font-medium\"\n          >\n            Sign in\n          </button>\n        </p>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAaO,MAAM,eAA4C,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE;IACtF,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,QAAQ;IACV;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,kBAAkB;QAClB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1C,UAAU,IAAI,GAAG;QACnB;QAEA,mBAAmB;QACnB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,GAAG;YACzC,UAAU,KAAK,GAAG;QACpB;QAEA,sBAAsB;QACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO;YACL,MAAM,qBAAqB,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,QAAQ;YAC7D,IAAI,CAAC,mBAAmB,OAAO,EAAE;gBAC/B,UAAU,QAAQ,GAAG,mBAAmB,MAAM,CAAC,EAAE;YACnD;QACF;QAEA,8BAA8B;QAC9B,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,UAAU,eAAe,GAAG;QAC9B,OAAO,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YACzD,UAAU,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,UAAU,MAAM,SACpB,SAAS,IAAI,CAAC,IAAI,IAClB,SAAS,KAAK,CAAC,IAAI,IACnB,SAAS,QAAQ,EACjB,SAAS,MAAM;QAGjB,IAAI,SAAS;YACX,UAAU,SAAS,KAAK,CAAC,IAAI;QAC/B;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAW,CAAC,kBAAkB,EAAE,OAAO,IAAI,GAAG,mBAAmB,IAAI;wCACrE,aAAY;;;;;;;;;;;;4BAGf,OAAO,IAAI,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI;;;;;;;;;;;;kCAIvE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAW,CAAC,kBAAkB,EAAE,OAAO,KAAK,GAAG,mBAAmB,IAAI;wCACtE,aAAY;;;;;;;;;;;;4BAGf,OAAO,KAAK,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAIzE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAA+C;;;;;;0CAGjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCACC,IAAG;wCACH,OAAO,SAAS,MAAM;wCACtB,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC3D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAoB;;;;;;;;;;;;;;;;;;;;;;;;kCAMxC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,IAAG;wCACH,MAAM,eAAe,SAAS;wCAC9B,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAW,CAAC,wBAAwB,EAAE,OAAO,QAAQ,GAAG,mBAAmB,IAAI;wCAC/E,aAAY;;;;;;kDAEd,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;qGAAe,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAGnE,OAAO,QAAQ,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAI/E,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAkB,WAAU;0CAA+C;;;;;;0CAG1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,IAAG;wCACH,MAAM,sBAAsB,SAAS;wCACrC,OAAO,SAAS,eAAe;wCAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCACpE,WAAW,CAAC,wBAAwB,EAAE,OAAO,eAAe,GAAG,mBAAmB,IAAI;wCACtF,aAAY;;;;;;kDAEd,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,uBAAuB,CAAC;wCACvC,WAAU;kDAET,oCAAsB,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;qGAAe,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAG1E,OAAO,eAAe,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,eAAe;;;;;;;;;;;;kCAI7F,8OAAC,0IAAA,CAAA,gBAAa;wBACZ,MAAK;wBACL,WAAW,QAAQ,SAAS;wBAC5B,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAgB;wBACF;sCACzB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/components/auth/LoginForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LoadingButton } from '@/components/ui/LoadingSpinner';\nimport { validateEmail } from '@/lib/utils';\nimport { Eye, EyeOff, Mail, Lock } from 'lucide-react';\n\ninterface LoginFormProps {\n  onSuccess: () => void;\n  onSwitchToRegister: () => void;\n}\n\nexport const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onSwitchToRegister }) => {\n  const { login, loading } = useAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [rememberMe, setRememberMe] = useState(false);\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    // Email validation\n    if (!formData.email) {\n      newErrors.email = 'Email is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Password validation\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    const success = await login(formData.email.trim(), formData.password);\n    if (success) {\n      onSuccess();\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  return (\n    <div className=\"card max-w-md mx-auto\">\n      <div className=\"text-center mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">Welcome Back</h2>\n        <p className=\"text-gray-600 mt-2\">Sign in to your Face App account</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* Email Field */}\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email Address\n          </label>\n          <div className=\"relative\">\n            <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              id=\"email\"\n              type=\"email\"\n              value={formData.email}\n              onChange={(e) => handleInputChange('email', e.target.value)}\n              className={`input-field pl-10 ${errors.email ? 'border-red-500' : ''}`}\n              placeholder=\"Enter your email\"\n              autoComplete=\"email\"\n            />\n          </div>\n          {errors.email && <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>}\n        </div>\n\n        {/* Password Field */}\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Password\n          </label>\n          <div className=\"relative\">\n            <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n            <input\n              id=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              value={formData.password}\n              onChange={(e) => handleInputChange('password', e.target.value)}\n              className={`input-field pl-10 pr-10 ${errors.password ? 'border-red-500' : ''}`}\n              placeholder=\"Enter your password\"\n              autoComplete=\"current-password\"\n            />\n            <button\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n            >\n              {showPassword ? <EyeOff className=\"w-5 h-5\" /> : <Eye className=\"w-5 h-5\" />}\n            </button>\n          </div>\n          {errors.password && <p className=\"text-red-500 text-sm mt-1\">{errors.password}</p>}\n        </div>\n\n        {/* Remember Me & Forgot Password */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <input\n              id=\"remember-me\"\n              type=\"checkbox\"\n              checked={rememberMe}\n              onChange={(e) => setRememberMe(e.target.checked)}\n              className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-700\">\n              Remember me\n            </label>\n          </div>\n          <button\n            type=\"button\"\n            className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\"\n          >\n            Forgot password?\n          </button>\n        </div>\n\n        {/* Submit Button */}\n        <LoadingButton\n          type=\"submit\"\n          isLoading={loading.isLoading}\n          className=\"w-full\"\n        >\n          Sign In\n        </LoadingButton>\n      </form>\n\n      {/* Divider */}\n      <div className=\"relative my-6\">\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"w-full border-t border-gray-300\" />\n        </div>\n        <div className=\"relative flex justify-center text-sm\">\n          <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n        </div>\n      </div>\n\n      {/* Social Login Buttons */}\n      <div className=\"space-y-3\">\n        <button\n          type=\"button\"\n          className=\"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200\"\n        >\n          <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n            <path\n              fill=\"currentColor\"\n              d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n            />\n            <path\n              fill=\"currentColor\"\n              d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n            />\n            <path\n              fill=\"currentColor\"\n              d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n            />\n            <path\n              fill=\"currentColor\"\n              d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n            />\n          </svg>\n          Continue with Google\n        </button>\n      </div>\n\n      {/* Switch to Register */}\n      <div className=\"text-center mt-6\">\n        <p className=\"text-gray-600\">\n          Don't have an account?{' '}\n          <button\n            onClick={onSwitchToRegister}\n            className=\"text-blue-600 hover:text-blue-700 font-medium\"\n          >\n            Create account\n          </button>\n        </p>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAaO,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE;IACnF,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,mBAAmB;QACnB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,GAAG;YACzC,UAAU,KAAK,GAAG;QACpB;QAEA,sBAAsB;QACtB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,UAAU,MAAM,MAAM,SAAS,KAAK,CAAC,IAAI,IAAI,SAAS,QAAQ;QACpE,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAW,CAAC,kBAAkB,EAAE,OAAO,KAAK,GAAG,mBAAmB,IAAI;wCACtE,aAAY;wCACZ,cAAa;;;;;;;;;;;;4BAGhB,OAAO,KAAK,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAIzE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCACC,IAAG;wCACH,MAAM,eAAe,SAAS;wCAC9B,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAW,CAAC,wBAAwB,EAAE,OAAO,QAAQ,GAAG,mBAAmB,IAAI;wCAC/E,aAAY;wCACZ,cAAa;;;;;;kDAEf,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;qGAAe,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAGnE,OAAO,QAAQ,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAI/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;wCAC/C,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAmC;;;;;;;;;;;;0CAI5E,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC,0IAAA,CAAA,gBAAa;wBACZ,MAAK;wBACL,WAAW,QAAQ,SAAS;wBAC5B,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAA8B;;;;;;;;;;;;;;;;;0BAKlD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;4BAAe,SAAQ;;8CACpC,8OAAC;oCACC,MAAK;oCACL,GAAE;;;;;;8CAEJ,8OAAC;oCACC,MAAK;oCACL,GAAE;;;;;;8CAEJ,8OAAC;oCACC,MAAK;oCACL,GAAE;;;;;;8CAEJ,8OAAC;oCACC,MAAK;oCACL,GAAE;;;;;;;;;;;;wBAEA;;;;;;;;;;;;0BAMV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAgB;wBACJ;sCACvB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/components/auth/OTPForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LoadingButton } from '@/components/ui/LoadingSpinner';\nimport { Mail, ArrowLeft, RefreshCw } from 'lucide-react';\n\ninterface OTPFormProps {\n  email: string;\n  onSuccess: () => void;\n  onBack: () => void;\n}\n\nexport const OTPForm: React.FC<OTPFormProps> = ({ email, onSuccess, onBack }) => {\n  const { verifyOTP, resendOTP, loading } = useAuth();\n  const [otp, setOtp] = useState(['', '', '', '', '', '']);\n  const [error, setError] = useState('');\n  const [resendCooldown, setResendCooldown] = useState(0);\n  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);\n\n  // Resend cooldown timer\n  useEffect(() => {\n    if (resendCooldown > 0) {\n      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [resendCooldown]);\n\n  const handleOtpChange = (index: number, value: string) => {\n    // Only allow digits\n    if (!/^\\d*$/.test(value)) return;\n\n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n    setError('');\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      inputRefs.current[index + 1]?.focus();\n    }\n  };\n\n  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {\n    if (e.key === 'Backspace' && !otp[index] && index > 0) {\n      inputRefs.current[index - 1]?.focus();\n    }\n  };\n\n  const handlePaste = (e: React.ClipboardEvent) => {\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData('text').replace(/\\D/g, '');\n    \n    if (pastedData.length === 6) {\n      const newOtp = pastedData.split('');\n      setOtp(newOtp);\n      setError('');\n      inputRefs.current[5]?.focus();\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const otpString = otp.join('');\n    if (otpString.length !== 6) {\n      setError('Please enter the complete 6-digit OTP');\n      return;\n    }\n\n    const success = await verifyOTP(email, otpString);\n    if (success) {\n      onSuccess();\n    }\n  };\n\n  const handleResendOTP = async () => {\n    if (resendCooldown > 0) return;\n    \n    const success = await resendOTP(email);\n    if (success) {\n      setResendCooldown(60); // 60 seconds cooldown\n      setOtp(['', '', '', '', '', '']);\n      inputRefs.current[0]?.focus();\n    }\n  };\n\n  return (\n    <div className=\"card max-w-md mx-auto\">\n      <div className=\"text-center mb-6\">\n        <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n          <Mail className=\"w-8 h-8 text-blue-600\" />\n        </div>\n        <h2 className=\"text-2xl font-bold text-gray-900\">Verify Your Email</h2>\n        <p className=\"text-gray-600 mt-2\">\n          We've sent a 6-digit verification code to\n        </p>\n        <p className=\"text-blue-600 font-medium\">{email}</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* OTP Input */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-3 text-center\">\n            Enter verification code\n          </label>\n          <div className=\"flex justify-center space-x-2\">\n            {otp.map((digit, index) => (\n              <input\n                key={index}\n                ref={(el) => (inputRefs.current[index] = el)}\n                type=\"text\"\n                maxLength={1}\n                value={digit}\n                onChange={(e) => handleOtpChange(index, e.target.value)}\n                onKeyDown={(e) => handleKeyDown(index, e)}\n                onPaste={handlePaste}\n                className={`w-12 h-12 text-center text-lg font-semibold border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${\n                  error ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0\"\n              />\n            ))}\n          </div>\n          {error && <p className=\"text-red-500 text-sm mt-2 text-center\">{error}</p>}\n        </div>\n\n        {/* Submit Button */}\n        <LoadingButton\n          type=\"submit\"\n          isLoading={loading.isLoading}\n          className=\"w-full\"\n        >\n          Verify Email\n        </LoadingButton>\n      </form>\n\n      {/* Resend OTP */}\n      <div className=\"text-center mt-6 space-y-3\">\n        <p className=\"text-gray-600\">Didn't receive the code?</p>\n        <button\n          onClick={handleResendOTP}\n          disabled={resendCooldown > 0 || loading.isLoading}\n          className=\"text-blue-600 hover:text-blue-700 font-medium disabled:text-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-1\"\n        >\n          <RefreshCw className=\"w-4 h-4\" />\n          <span>\n            {resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Code'}\n          </span>\n        </button>\n      </div>\n\n      {/* Back Button */}\n      <div className=\"text-center mt-6\">\n        <button\n          onClick={onBack}\n          className=\"text-gray-600 hover:text-gray-700 font-medium flex items-center justify-center space-x-1 mx-auto\"\n        >\n          <ArrowLeft className=\"w-4 h-4\" />\n          <span>Back to Registration</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAaO,MAAM,UAAkC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;IAC1E,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA+B,EAAE;IAExD,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,GAAG;YACtB,MAAM,QAAQ,WAAW,IAAM,kBAAkB,iBAAiB,IAAI;YACtE,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,kBAAkB,CAAC,OAAe;QACtC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;QAE1B,MAAM,SAAS;eAAI;SAAI;QACvB,MAAM,CAAC,MAAM,GAAG;QAChB,OAAO;QACP,SAAS;QAET,wBAAwB;QACxB,IAAI,SAAS,QAAQ,GAAG;YACtB,UAAU,OAAO,CAAC,QAAQ,EAAE,EAAE;QAChC;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,GAAG,CAAC,MAAM,IAAI,QAAQ,GAAG;YACrD,UAAU,OAAO,CAAC,QAAQ,EAAE,EAAE;QAChC;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,MAAM,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC,QAAQ,OAAO,CAAC,OAAO;QAElE,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,MAAM,SAAS,WAAW,KAAK,CAAC;YAChC,OAAO;YACP,SAAS;YACT,UAAU,OAAO,CAAC,EAAE,EAAE;QACxB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,MAAM,YAAY,IAAI,IAAI,CAAC;QAC3B,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,SAAS;YACT;QACF;QAEA,MAAM,UAAU,MAAM,UAAU,OAAO;QACvC,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,GAAG;QAExB,MAAM,UAAU,MAAM,UAAU;QAChC,IAAI,SAAS;YACX,kBAAkB,KAAK,sBAAsB;YAC7C,OAAO;gBAAC;gBAAI;gBAAI;gBAAI;gBAAI;gBAAI;aAAG;YAC/B,UAAU,OAAO,CAAC,EAAE,EAAE;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG5C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA2D;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;0CACZ,IAAI,GAAG,CAAC,CAAC,OAAO,sBACf,8OAAC;wCAEC,KAAK,CAAC,KAAQ,UAAU,OAAO,CAAC,MAAM,GAAG;wCACzC,MAAK;wCACL,WAAW;wCACX,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;wCACtD,WAAW,CAAC,IAAM,cAAc,OAAO;wCACvC,SAAS;wCACT,WAAW,CAAC,uKAAuK,EACjL,QAAQ,mBAAmB,mBAC3B;wCACF,aAAY;uCAXP;;;;;;;;;;4BAeV,uBAAS,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;kCAIlE,8OAAC,0IAAA,CAAA,gBAAa;wBACZ,MAAK;wBACL,WAAW,QAAQ,SAAS;wBAC5B,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBACC,SAAS;wBACT,UAAU,iBAAiB,KAAK,QAAQ,SAAS;wBACjD,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CACE,iBAAiB,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;0BAM7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/app/auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { RegisterForm } from '@/components/auth/RegisterForm';\nimport { LoginForm } from '@/components/auth/LoginForm';\nimport { OTPForm } from '@/components/auth/OTPForm';\nimport { LoadingOverlay } from '@/components/ui/LoadingSpinner';\nimport { Palette, Sparkles, Users, Shield } from 'lucide-react';\n\ntype AuthStep = 'login' | 'register' | 'otp';\n\nexport default function AuthPage() {\n  const router = useRouter();\n  const { isAuthenticated, loading } = useAuth();\n  const [currentStep, setCurrentStep] = useState<AuthStep>('login');\n  const [registrationEmail, setRegistrationEmail] = useState('');\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleRegistrationSuccess = (email: string) => {\n    setRegistrationEmail(email);\n    setCurrentStep('otp');\n  };\n\n  const handleOTPSuccess = () => {\n    setCurrentStep('login');\n  };\n\n  const handleLoginSuccess = () => {\n    router.push('/dashboard');\n  };\n\n  if (loading.isLoading) {\n    return <LoadingOverlay message={loading.message} />;\n  }\n\n  if (isAuthenticated) {\n    return null; // Will redirect\n  }\n\n  return (\n    <div className=\"min-h-screen gradient-bg flex\">\n      {/* Left Side - Features */}\n      <div className=\"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white p-12 flex-col justify-center\">\n        <div className=\"max-w-md\">\n          <h1 className=\"text-4xl font-bold mb-6\">\n            Discover Your Perfect Colors\n          </h1>\n          <p className=\"text-xl mb-8 text-blue-100\">\n            Get personalized color recommendations based on your unique facial features and skin tone.\n          </p>\n          \n          <div className=\"space-y-6\">\n            <div className=\"flex items-start space-x-4\">\n              <div className=\"bg-white bg-opacity-20 rounded-lg p-3\">\n                <Palette className=\"w-6 h-6\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-1\">AI-Powered Analysis</h3>\n                <p className=\"text-blue-100\">\n                  Advanced facial recognition technology analyzes your skin tone and features\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start space-x-4\">\n              <div className=\"bg-white bg-opacity-20 rounded-lg p-3\">\n                <Sparkles className=\"w-6 h-6\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-1\">Personalized Recommendations</h3>\n                <p className=\"text-blue-100\">\n                  Get outfit suggestions tailored to your unique color palette\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start space-x-4\">\n              <div className=\"bg-white bg-opacity-20 rounded-lg p-3\">\n                <Users className=\"w-6 h-6\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-1\">Style Community</h3>\n                <p className=\"text-blue-100\">\n                  Join thousands of users discovering their perfect style\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start space-x-4\">\n              <div className=\"bg-white bg-opacity-20 rounded-lg p-3\">\n                <Shield className=\"w-6 h-6\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold mb-1\">Privacy First</h3>\n                <p className=\"text-blue-100\">\n                  Your photos are processed securely and never shared\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Right Side - Auth Forms */}\n      <div className=\"w-full lg:w-1/2 flex items-center justify-center p-8\">\n        <div className=\"w-full max-w-md\">\n          {currentStep === 'login' && (\n            <LoginForm\n              onSuccess={handleLoginSuccess}\n              onSwitchToRegister={() => setCurrentStep('register')}\n            />\n          )}\n          \n          {currentStep === 'register' && (\n            <RegisterForm\n              onSuccess={handleRegistrationSuccess}\n              onSwitchToLogin={() => setCurrentStep('login')}\n            />\n          )}\n          \n          {currentStep === 'otp' && (\n            <OTPForm\n              email={registrationEmail}\n              onSuccess={handleOTPSuccess}\n              onBack={() => setCurrentStep('register')}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,4BAA4B,CAAC;QACjC,qBAAqB;QACrB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,QAAQ,SAAS,EAAE;QACrB,qBAAO,8OAAC,0IAAA,CAAA,iBAAc;YAAC,SAAS,QAAQ,OAAO;;;;;;IACjD;IAEA,IAAI,iBAAiB;QACnB,OAAO,MAAM,gBAAgB;IAC/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAMjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAMjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAMjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,gBAAgB,yBACf,8OAAC,uIAAA,CAAA,YAAS;4BACR,WAAW;4BACX,oBAAoB,IAAM,eAAe;;;;;;wBAI5C,gBAAgB,4BACf,8OAAC,0IAAA,CAAA,eAAY;4BACX,WAAW;4BACX,iBAAiB,IAAM,eAAe;;;;;;wBAIzC,gBAAgB,uBACf,8OAAC,qIAAA,CAAA,UAAO;4BACN,OAAO;4BACP,WAAW;4BACX,QAAQ,IAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}