'use client';

import React, { useState } from 'react';
import { authAPI } from '@/lib/api';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { CheckCircle, XCircle, User, Mail, Lock } from 'lucide-react';
import toast from 'react-hot-toast';

export const AuthTest: React.FC = () => {
  const [testData, setTestData] = useState({
    name: 'Test User',
    email: '<EMAIL>',
    password: 'TestPass123',
    gender: 'male',
    otp: '123456'
  });
  const [isTestingRegister, setIsTestingRegister] = useState(false);
  const [isTestingOTP, setIsTestingOTP] = useState(false);
  const [isTestingLogin, setIsTestingLogin] = useState(false);
  const [registerResult, setRegisterResult] = useState<any>(null);
  const [otpResult, setOtpResult] = useState<any>(null);
  const [loginResult, setLoginResult] = useState<any>(null);

  const testRegister = async () => {
    try {
      setIsTestingRegister(true);
      const result = await authAPI.register({
        name: testData.name,
        email: testData.email,
        password: testData.password,
        gender: testData.gender as any
      });
      setRegisterResult(result);
      if (result.success) {
        toast.success('Registration test successful!');
      } else {
        toast.error('Registration test failed');
      }
    } catch (error: any) {
      console.error('Registration test failed:', error);
      setRegisterResult({ 
        success: false, 
        error: error.response?.data?.message || error.message 
      });
      toast.error('Registration test failed');
    } finally {
      setIsTestingRegister(false);
    }
  };

  const testOTP = async () => {
    try {
      setIsTestingOTP(true);
      const result = await authAPI.verifyOTP({
        email: testData.email,
        otp: testData.otp
      });
      setOtpResult(result);
      if (result.success) {
        toast.success('OTP test successful!');
      } else {
        toast.error('OTP test failed');
      }
    } catch (error: any) {
      console.error('OTP test failed:', error);
      setOtpResult({ 
        success: false, 
        error: error.response?.data?.message || error.message 
      });
      toast.error('OTP test failed');
    } finally {
      setIsTestingOTP(false);
    }
  };

  const testLogin = async () => {
    try {
      setIsTestingLogin(true);
      const result = await authAPI.login({
        email: testData.email,
        password: testData.password
      });
      setLoginResult(result);
      if (result.success) {
        toast.success('Login test successful!');
      } else {
        toast.error('Login test failed');
      }
    } catch (error: any) {
      console.error('Login test failed:', error);
      setLoginResult({ 
        success: false, 
        error: error.response?.data?.message || error.message 
      });
      toast.error('Login test failed');
    } finally {
      setIsTestingLogin(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setTestData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="card max-w-4xl mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
        <User className="w-6 h-6 text-purple-600" />
        <span>Authentication API Test</span>
      </h2>

      {/* Test Data Form */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Test Data</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={testData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              value={testData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
            <input
              type="password"
              value={testData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">OTP</label>
            <input
              type="text"
              value={testData.otp}
              onChange={(e) => handleInputChange('otp', e.target.value)}
              className="input-field"
              placeholder="6-digit OTP"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Test Actions</h3>
          
          <LoadingButton
            onClick={testRegister}
            isLoading={isTestingRegister}
            className="w-full"
          >
            Test Registration
          </LoadingButton>
          
          <LoadingButton
            onClick={testOTP}
            isLoading={isTestingOTP}
            className="w-full"
          >
            Test OTP Verification
          </LoadingButton>
          
          <LoadingButton
            onClick={testLogin}
            isLoading={isTestingLogin}
            className="w-full"
          >
            Test Login
          </LoadingButton>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-6">
        {/* Registration Result */}
        {registerResult && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Registration Result</h4>
            <div className={`p-4 rounded-lg border ${
              registerResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {registerResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`font-medium ${
                  registerResult.success ? 'text-green-900' : 'text-red-900'
                }`}>
                  {registerResult.success ? 'Registration successful' : 'Registration failed'}
                </span>
              </div>
              <pre className="text-sm text-gray-700 bg-white p-2 rounded border overflow-auto">
                {JSON.stringify(registerResult, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* OTP Result */}
        {otpResult && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">OTP Verification Result</h4>
            <div className={`p-4 rounded-lg border ${
              otpResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {otpResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`font-medium ${
                  otpResult.success ? 'text-green-900' : 'text-red-900'
                }`}>
                  {otpResult.success ? 'OTP verification successful' : 'OTP verification failed'}
                </span>
              </div>
              <pre className="text-sm text-gray-700 bg-white p-2 rounded border overflow-auto">
                {JSON.stringify(otpResult, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Login Result */}
        {loginResult && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Login Result</h4>
            <div className={`p-4 rounded-lg border ${
              loginResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {loginResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`font-medium ${
                  loginResult.success ? 'text-green-900' : 'text-red-900'
                }`}>
                  {loginResult.success ? 'Login successful' : 'Login failed'}
                </span>
              </div>
              <pre className="text-sm text-gray-700 bg-white p-2 rounded border overflow-auto">
                {JSON.stringify(loginResult, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
