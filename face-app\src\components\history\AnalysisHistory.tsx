'use client';

import React, { useState, useEffect } from 'react';
import { FaceAnalysis } from '@/types';
import { faceAPI } from '@/lib/api';
import { formatDate, formatConfidence } from '@/lib/utils';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  Calendar, 
  Eye, 
  Trash2, 
  Download, 
  Search,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import toast from 'react-hot-toast';

interface AnalysisHistoryProps {
  onSelectAnalysis?: (analysis: FaceAnalysis) => void;
}

export const AnalysisHistory: React.FC<AnalysisHistoryProps> = ({ onSelectAnalysis }) => {
  const [analyses, setAnalyses] = useState<FaceAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedAnalyses, setSelectedAnalyses] = useState<string[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);

  const itemsPerPage = 6;

  useEffect(() => {
    loadHistory();
  }, [currentPage]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const response = await faceAPI.getHistory(currentPage, itemsPerPage);
      
      if (response.success && response.data) {
        setAnalyses(response.data.analyses);
        // Calculate total pages (assuming API doesn't return this info)
        setTotalPages(Math.ceil(response.data.analyses.length / itemsPerPage));
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to load history');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAnalysis = async (id: string) => {
    if (!confirm('Are you sure you want to delete this analysis?')) return;

    try {
      setIsDeleting(true);
      const response = await faceAPI.deleteAnalysis(id);
      
      if (response.success) {
        setAnalyses(prev => prev.filter(analysis => analysis._id !== id));
        toast.success('Analysis deleted successfully');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to delete analysis');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedAnalyses.length === 0) return;
    if (!confirm(`Are you sure you want to delete ${selectedAnalyses.length} analyses?`)) return;

    try {
      setIsDeleting(true);
      await Promise.all(selectedAnalyses.map(id => faceAPI.deleteAnalysis(id)));
      setAnalyses(prev => prev.filter(analysis => !selectedAnalyses.includes(analysis._id)));
      setSelectedAnalyses([]);
      toast.success(`${selectedAnalyses.length} analyses deleted successfully`);
    } catch (error: any) {
      toast.error('Failed to delete some analyses');
    } finally {
      setIsDeleting(false);
    }
  };

  const toggleSelectAnalysis = (id: string) => {
    setSelectedAnalyses(prev =>
      prev.includes(id) ? prev.filter(analysisId => analysisId !== id) : [...prev, id]
    );
  };

  const filteredAnalyses = analyses.filter(analysis =>
    analysis.facialFeatures.faceShape.toLowerCase().includes(searchTerm.toLowerCase()) ||
    formatDate(analysis.createdAt).toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-2xl font-bold text-gray-900">Analysis History</h2>
        
        {selectedAnalyses.length > 0 && (
          <button
            onClick={handleBulkDelete}
            disabled={isDeleting}
            className="btn-secondary text-red-600 hover:bg-red-50 flex items-center space-x-2"
          >
            <Trash2 className="w-4 h-4" />
            <span>Delete Selected ({selectedAnalyses.length})</span>
          </button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search by face shape or date..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input-field pl-10"
          />
        </div>
        <button className="btn-secondary flex items-center space-x-2">
          <Filter className="w-4 h-4" />
          <span>Filter</span>
        </button>
      </div>

      {/* Analysis Grid */}
      {filteredAnalyses.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Calendar className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No analyses found</h3>
          <p className="text-gray-600">
            {searchTerm ? 'Try adjusting your search terms' : 'Start by uploading your first photo'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAnalyses.map((analysis) => (
            <div key={analysis._id} className="card hover:shadow-lg transition-shadow duration-200">
              {/* Selection Checkbox */}
              <div className="flex items-center justify-between mb-3">
                <input
                  type="checkbox"
                  checked={selectedAnalyses.includes(analysis._id)}
                  onChange={() => toggleSelectAnalysis(analysis._id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-green-600 font-medium">
                    {formatConfidence(analysis.confidence)}
                  </span>
                </div>
              </div>

              {/* Image */}
              <div className="relative mb-4">
                <img
                  src={analysis.imageUrl}
                  alt="Analysis"
                  className="w-full h-48 object-cover rounded-lg"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                  <button
                    onClick={() => onSelectAnalysis?.(analysis)}
                    className="opacity-0 hover:opacity-100 bg-white text-gray-900 px-4 py-2 rounded-lg font-medium transition-opacity duration-200"
                  >
                    View Details
                  </button>
                </div>
              </div>

              {/* Analysis Info */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Face Shape</span>
                  <span className="font-medium capitalize">{analysis.facialFeatures.faceShape}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Eye Shape</span>
                  <span className="font-medium capitalize">{analysis.facialFeatures.eyeShape}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Date</span>
                  <span className="font-medium">{formatDate(analysis.createdAt)}</span>
                </div>
              </div>

              {/* Color Preview */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">Colors</span>
                </div>
                <div className="flex space-x-2">
                  <div
                    className="w-6 h-6 rounded-full border border-gray-200"
                    style={{ backgroundColor: analysis.colors.skinTone }}
                    title="Skin Tone"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-gray-200"
                    style={{ backgroundColor: analysis.colors.eyeColor }}
                    title="Eye Color"
                  />
                  <div
                    className="w-6 h-6 rounded-full border border-gray-200"
                    style={{ backgroundColor: analysis.colors.hairColor }}
                    title="Hair Color"
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between">
                <button
                  onClick={() => onSelectAnalysis?.(analysis)}
                  className="text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1"
                >
                  <Eye className="w-4 h-4" />
                  <span>View</span>
                </button>
                
                <div className="flex items-center space-x-2">
                  <button
                    className="text-gray-600 hover:text-gray-700 p-1"
                    title="Download"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteAnalysis(analysis._id)}
                    disabled={isDeleting}
                    className="text-red-600 hover:text-red-700 p-1"
                    title="Delete"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-4">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="btn-secondary flex items-center space-x-2 disabled:opacity-50"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>
          
          <span className="text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="btn-secondary flex items-center space-x-2 disabled:opacity-50"
          >
            <span>Next</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
};
