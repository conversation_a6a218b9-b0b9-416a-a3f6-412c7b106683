{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { uploadAPI } from './api';\nimport { CloudinaryUploadResult } from '@/types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Image validation\nexport const validateImageFile = (file: File): { isValid: boolean; error?: string } => {\n  const maxSize = 10 * 1024 * 1024; // 10MB\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n\n  if (!allowedTypes.includes(file.type)) {\n    return {\n      isValid: false,\n      error: 'Please select a valid image file (JPEG, PNG, or WebP)',\n    };\n  }\n\n  if (file.size > maxSize) {\n    return {\n      isValid: false,\n      error: 'Image size must be less than 10MB',\n    };\n  }\n\n  return { isValid: true };\n};\n\n// Upload image to Cloudinary\nexport const uploadImageToCloudinary = async (file: File): Promise<CloudinaryUploadResult> => {\n  try {\n    // Get upload signature from backend\n    const signatureResponse = await uploadAPI.getUploadSignature();\n    if (!signatureResponse.success || !signatureResponse.data) {\n      throw new Error('Failed to get upload signature');\n    }\n\n    const { signature, timestamp, apiKey, uploadUrl, folder } = signatureResponse.data;\n\n    // Create form data for Cloudinary upload\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('signature', signature);\n    formData.append('timestamp', timestamp.toString());\n    formData.append('api_key', apiKey);\n    formData.append('folder', folder);\n\n    // Upload to Cloudinary\n    const uploadResponse = await fetch(uploadUrl, {\n      method: 'POST',\n      body: formData,\n    });\n\n    if (!uploadResponse.ok) {\n      throw new Error('Failed to upload image');\n    }\n\n    const result: CloudinaryUploadResult = await uploadResponse.json();\n    return result;\n  } catch (error) {\n    console.error('Upload error:', error);\n    throw new Error('Failed to upload image. Please try again.');\n  }\n};\n\n// Format date\nexport const formatDate = (dateString: string): string => {\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\n// Format confidence percentage\nexport const formatConfidence = (confidence: number): string => {\n  return `${(confidence * 100).toFixed(1)}%`;\n};\n\n// Color utilities\nexport const isLightColor = (hex: string): boolean => {\n  const r = parseInt(hex.slice(1, 3), 16);\n  const g = parseInt(hex.slice(3, 5), 16);\n  const b = parseInt(hex.slice(5, 7), 16);\n  const brightness = (r * 299 + g * 587 + b * 114) / 1000;\n  return brightness > 128;\n};\n\nexport const getContrastColor = (hex: string): string => {\n  return isLightColor(hex) ? '#000000' : '#ffffff';\n};\n\n// Debounce function\nexport const debounce = <T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n};\n\n// Local storage utilities\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null;\n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : defaultValue || null;\n    } catch {\n      return defaultValue || null;\n    }\n  },\n\n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return;\n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error);\n    }\n  },\n\n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return;\n    localStorage.removeItem(key);\n  },\n};\n\n// Error handling\nexport const getErrorMessage = (error: any): string => {\n  if (error?.response?.data?.message) {\n    return error.response.data.message;\n  }\n  if (error?.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n};\n\n// Validation utilities\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {\n  const errors: string[] = [];\n  \n  if (password.length < 6) {\n    errors.push('Password must be at least 6 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n};\n\n// Generate random ID\nexport const generateId = (): string => {\n  return Math.random().toString(36).substr(2, 9);\n};\n\n// Copy to clipboard\nexport const copyToClipboard = async (text: string): Promise<boolean> => {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.select();\n    const success = document.execCommand('copy');\n    document.body.removeChild(textArea);\n    return success;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,oBAAoB,CAAC;IAChC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAE3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACrC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI,KAAK,IAAI,GAAG,SAAS;QACvB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,oCAAoC;QACpC,MAAM,oBAAoB,MAAM,iHAAA,CAAA,YAAS,CAAC,kBAAkB;QAC5D,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,kBAAkB,IAAI;QAElF,yCAAyC;QACzC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,aAAa;QAC7B,SAAS,MAAM,CAAC,aAAa,UAAU,QAAQ;QAC/C,SAAS,MAAM,CAAC,WAAW;QAC3B,SAAS,MAAM,CAAC,UAAU;QAE1B,uBAAuB;QACvB,MAAM,iBAAiB,MAAM,MAAM,WAAW;YAC5C,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAiC,MAAM,eAAe,IAAI;QAChE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,GAAG,CAAC,aAAa,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5C;AAGO,MAAM,eAAe,CAAC;IAC3B,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IACpC,MAAM,aAAa,CAAC,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI;IACnD,OAAO,aAAa;AACtB;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,OAAO,YAAY;AACzC;AAGO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;;IAO5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;;IAMrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;;IAErC;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,UAAU,MAAM,SAAS;QAClC,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;IACpC;IACA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IACA,OAAO;AACT;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,aAAa;IACxB,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAM;QACN,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,MAAM;QACf,MAAM,UAAU,SAAS,WAAW,CAAC;QACrC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8',\n  };\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n};\n\ninterface LoadingOverlayProps {\n  message?: string;\n  className?: string;\n}\n\nexport const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ \n  message = 'Loading...', \n  className \n}) => {\n  return (\n    <div className={cn(\n      'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',\n      className\n    )}>\n      <div className=\"bg-white rounded-lg p-6 flex flex-col items-center space-y-4\">\n        <LoadingSpinner size=\"lg\" />\n        <p className=\"text-gray-700 font-medium\">{message}</p>\n      </div>\n    </div>\n  );\n};\n\ninterface LoadingButtonProps {\n  isLoading: boolean;\n  children: React.ReactNode;\n  className?: string;\n  disabled?: boolean;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n}\n\nexport const LoadingButton: React.FC<LoadingButtonProps> = ({\n  isLoading,\n  children,\n  className,\n  disabled,\n  onClick,\n  type = 'button',\n}) => {\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || isLoading}\n      className={cn(\n        'btn-primary flex items-center justify-center space-x-2',\n        className\n      )}\n    >\n      {isLoading && <LoadingSpinner size=\"sm\" className=\"border-white border-t-transparent\" />}\n      <span>{children}</span>\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AACA;;;AAOO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;AAOO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,YAAY,EACtB,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8EACA;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAe,MAAK;;;;;;8BACrB,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;AAWO,MAAM,gBAA8C,CAAC,EAC1D,SAAS,EACT,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EAChB;IACC,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;;YAGD,2BAAa,8OAAC;gBAAe,MAAK;gBAAK,WAAU;;;;;;0BAClD,8OAAC;0BAAM;;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/myidea/website/face-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LoadingOverlay } from '@/components/ui/LoadingSpinner';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated, loading } = useAuth();\n\n  useEffect(() => {\n    if (!loading.isLoading) {\n      if (isAuthenticated) {\n        router.push('/dashboard');\n      } else {\n        router.push('/auth');\n      }\n    }\n  }, [isAuthenticated, loading.isLoading, router]);\n\n  return <LoadingOverlay message=\"Loading Face App...\" />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAiB,QAAQ,SAAS;QAAE;KAAO;IAE/C,qBAAO,8OAAC,0IAAA,CAAA,iBAAc;QAAC,SAAQ;;;;;;AACjC", "debugId": null}}]}