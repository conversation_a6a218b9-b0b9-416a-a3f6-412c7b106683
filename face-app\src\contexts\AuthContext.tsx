'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, LoadingState } from '@/types';
import { authAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  loading: LoadingState;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (name: string, email: string, password: string, gender: string) => Promise<boolean>;
  verifyOTP: (email: string, otp: string) => Promise<boolean>;
  resendOTP: (email: string) => Promise<boolean>;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<LoadingState>({ isLoading: true });

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    const token = localStorage.getItem('authToken');
    if (!token) {
      setLoading({ isLoading: false });
      return;
    }

    try {
      setLoading({ isLoading: true, message: 'Checking authentication...' });
      const response = await authAPI.getProfile();
      if (response.success && response.data) {
        setUser(response.data);
      } else {
        localStorage.removeItem('authToken');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('authToken');
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Signing in...' });
      const response = await authAPI.login({ email, password });
      
      if (response.success && response.data) {
        localStorage.setItem('authToken', response.data.token);
        setUser(response.data.user);
        toast.success('Login successful!');
        return true;
      } else {
        toast.error(response.message || 'Login failed');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Login failed';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const register = async (
    name: string,
    email: string,
    password: string,
    gender: string
  ): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Creating account...' });
      const response = await authAPI.register({
        name,
        email,
        password,
        gender: gender as any,
      });
      
      if (response.success) {
        toast.success(response.message || 'Registration successful! Please check your email for OTP.');
        return true;
      } else {
        toast.error(response.message || 'Registration failed');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const verifyOTP = async (email: string, otp: string): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Verifying OTP...' });
      const response = await authAPI.verifyOTP({ email, otp });
      
      if (response.success) {
        toast.success('Email verified successfully!');
        return true;
      } else {
        toast.error(response.message || 'OTP verification failed');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'OTP verification failed';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const resendOTP = async (email: string): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Resending OTP...' });
      const response = await authAPI.resendOTP(email);
      
      if (response.success) {
        toast.success(response.message || 'OTP sent successfully!');
        return true;
      } else {
        toast.error(response.message || 'Failed to resend OTP');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to resend OTP';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const logout = async () => {
    try {
      setLoading({ isLoading: true, message: 'Signing out...' });
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('authToken');
      setUser(null);
      setLoading({ isLoading: false });
      toast.success('Logged out successfully');
    }
  };

  const refreshUser = async () => {
    try {
      const response = await authAPI.getProfile();
      if (response.success && response.data) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    register,
    verifyOTP,
    resendOTP,
    isAuthenticated: !!user,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
