# Face App - AI-Powered Color Recommendations

A modern Next.js frontend application for AI-powered facial analysis and personalized color recommendations.

## Features

- **User Authentication**: Complete registration, login, and OTP verification system
- **Face Analysis**: Upload photos for AI-powered facial feature analysis
- **Color Recommendations**: Get personalized outfit and color suggestions
- **Analysis History**: View and manage past face analyses
- **User Profile**: Manage account settings and preferences
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

## Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom components with Lucide React icons
- **State Management**: React Context API
- **HTTP Client**: Axios
- **Notifications**: React Hot Toast
- **Image Upload**: Cloudinary integration

## API Integration

This frontend connects to the Face App API at `https://faceapp-ttwh.onrender.com/api` with the following endpoints:

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/verify-email-otp` - Email verification
- `POST /auth/login` - User login
- `GET /auth/me` - Get user profile
- `POST /auth/logout` - User logout
- `POST /auth/resend-verification` - Resend OTP

### Face Analysis
- `POST /face/analyze-direct` - Analyze uploaded face image
- `GET /face/history` - Get analysis history
- `GET /face/analysis/:id` - Get specific analysis
- `DELETE /face/analysis/:id` - Delete analysis

### Color Recommendations
- `POST /face/analysis/:id/recommendations` - Get color recommendations
- `GET /face/recommendations/latest` - Get latest recommendations
- `GET /face/recommendations/history` - Get recommendation history
- `POST /face/recommendations/:id/feedback` - Submit feedback

### File Upload
- `POST /upload/mobile-signature` - Get Cloudinary upload signature
- `GET /upload/config` - Get upload configuration

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd face-app
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser
